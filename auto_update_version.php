<?php
/**
 * 自动更新版本文件脚本
 * 每分钟执行一次，从远程服务器下载最新的版本信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境关闭错误显示

// 包含更新处理函数
require_once 'update_handler.php';

// 记录日志函数
function writeLog($message) {
    $logFile = './temp/auto_update.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 主执行逻辑
try {
    writeLog('开始自动版本检查');

    // 获取版本检查URL - 优先使用Now_user字段
    $versionFilePath = './temp/new.txt';
    $updateUrl = null;

    if (file_exists($versionFilePath)) {
        $currentVersionInfo = parseVersionFile($versionFilePath);
        if ($currentVersionInfo && isset($currentVersionInfo['Now_user']) && !empty($currentVersionInfo['Now_user'])) {
            $updateUrl = $currentVersionInfo['Now_user'];
            writeLog('使用Now_user URL检查版本: ' . $updateUrl);
        }
    }

    // 如果没有找到Now_user字段或字段为空，使用默认URL
    if (!$updateUrl) {
        $updateUrl = 'https://d.ly-y.cn/up/new.txt';
        writeLog('使用默认URL检查版本: ' . $updateUrl);
    }

    // 从Now_user或默认URL下载版本信息
    $result = downloadVersionFileWithDetailsFromUrl($updateUrl);

    if ($result['success']) {
        writeLog('版本信息获取成功，使用方法: ' . ($result['method'] ?? '未知'));

        // 检查是否有新版本
        if (file_exists($versionFilePath)) {
            $versionInfo = parseVersionFile($versionFilePath);
            if ($versionInfo) {
                $hasUpdate = ($versionInfo['New_version'] !== $versionInfo['Now_version']);
                if ($hasUpdate) {
                    writeLog('发现新版本: ' . $versionInfo['New_version'] . ' (当前版本: ' . $versionInfo['Now_version'] . ')');
                    writeLog('下载链接: ' . ($versionInfo['Download'] ?? '未知'));
                    writeLog('更新内容: ' . ($versionInfo['Update_content'] ?? '无'));
                    if (isset($versionInfo['Over_user'])) {
                        writeLog('更新完成后将使用URL: ' . $versionInfo['Over_user']);
                    }
                } else {
                    writeLog('当前已是最新版本: ' . $versionInfo['Now_version']);
                }
            } else {
                writeLog('版本文件解析失败');
            }
        } else {
            writeLog('版本文件不存在');
        }
    } else {
        writeLog('版本信息获取失败: ' . $result['error']);
    }

} catch (Exception $e) {
    writeLog('自动版本检查出错: ' . $e->getMessage());
}

writeLog('自动更新检查结束');
?>

<?php
// 获取当前版本信息
require_once 'update_handler.php';

/**
 * 获取当前实际安装的版本
 * 优先从本地版本记录获取，如果没有则从config.php获取
 */
function getCurrentVersion() {
    // 先尝试从本地版本记录获取
    $localVersion = getLocalInstalledVersion();
    if ($localVersion) {
        return $localVersion;
    }

    // 如果没有本地版本记录，从config.php获取
    if (file_exists('config.php')) {
        include 'config.php';
        if (isset($current_version)) {
            return $current_version;
        }
    }

    // 都没有的话返回默认版本
    return '1.0.0';
}

// 返回JSON格式的版本信息
header('Content-Type: application/json');
echo json_encode([
    'version' => getCurrentVersion()
]);
?>